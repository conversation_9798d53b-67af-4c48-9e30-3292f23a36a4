#!/bin/bash
#. /home/<USER>/Desktop/clonezilla-master/usr/sbin/ocs-flyright-restore

db_path="/home/<USER>/Desktop/DriveDatabase/FlyrightDriveDatabase.db"
echo start
DIA="whiptail"
ocs_sr="/usr/sbin/ocs-sr"
# Function to display an error message and exit
error_exit() {
    whiptail --title "Error" --msgbox "$1" 8 50
    exit 1
}



# Function to select a Simulator
select_simulator() {
    local simulators=$(sqlite3 "$db_path" "SELECT DISTINCT Simulator FROM drives;" | sed 's/\"/\\\"/g')
    local sim_menu=""

    # Build the whiptail menu for simulators
    while IFS= read -r sim; do
        sim_menu+="\"$sim\" \"$sim\" ";
    done <<< "$simulators"

    if [[ -z "$sim_menu" ]]; then
        error_exit "No simulators found in the database."
		exit 1
    fi

    local selected_sim=$(eval "whiptail --title \"Select Simulator\" --menu \"Choose a Simulator:\" 15 50 8 $sim_menu 3>&1 1>&2 2>&3")
      if [[ -v $selected_sim ]]; then
	  error_exit
      fi
      

    echo "$selected_sim"
}

# Function to select a Computer (treat multiple with the same name as one)
select_computer() {
    local simulator="$1"
    local computers=$(sqlite3 "$db_path" "SELECT DISTINCT Computer FROM drives WHERE Simulator = '$simulator';" | sed 's/\"/\\\"/g')
    local comp_menu=""

    # Build the whiptail menu for computers
    while IFS= read -r comp; do
        comp_menu+="\"$comp\" \"$comp\" ";
    done <<< "$computers"

    if [[ -z "$comp_menu" ]]; then
        error_exit "No computers found for Simulator '$simulator'."
		
    fi

    local selected_comp=$(eval "whiptail --title \"Select Computer\" --menu \"Choose a Computer:\" 15 50 8 $comp_menu 3>&1 1>&2 2>&3")

    if [[ $? -ne 0 || -z "$selected_comp" ]]; then
        error_exit "Operation canceled by user."
		local exit 1
    fi

    echo "$selected_comp"
}

select_at() {
    local simulator="$1"
	local computers="$2"
    local asset_tags=$(sqlite3 "$db_path" "SELECT DISTINCT \"Asset Tag\" FROM drives WHERE Simulator = '$simulator' AND Computer = '$computers';" | sed 's/\"/\\\"/g')
    local tag_menu=""


    # Build the whiptail menu for computers
    while IFS= read -r tag; do
        tag_menu+="\"$tag\" \"$tag\" ";
    done <<< "$asset_tags"

    if [[ -z "$tag_menu" ]]; then
        error_exit "No Asset Tags found for '$simulator' '$computers'."
		
    fi

    local selected_tag=$(eval "whiptail --title \"Select Asset Tag\" --menu \"Choose an Asset Tag:\" 15 50 8 $tag_menu 3>&1 1>&2 2>&3")

    if [[ $? -ne 0 || -z "$selected_tag" ]]; then
        error_exit "Operation canceled by user."
		local exit 1
    fi

    echo "$selected_tag"
}


# Function to add or update a row
add_row() {
    local simulator="$1"
    local computer="$2"
    local newdrive="$3"

    # Get Letter/PriSec
	local letter_prisec=$(whiptail --title "Select Letter/PriSec" \
    --menu "Choose a Letter/PriSec value:" 15 50 8 \
    "Primary" "" \
    "Secondary" "" \
    "Tertiary" "" \
    "A" "" \
    "B" "" \
    "C" "" \
    "D" "" \
    3>&1 1>&2 2>&3)
    if [[ $? -ne 0 || -z "$letter_prisec" ]]; then
        error_exit "Operation canceled by user."
		exit 1
    fi

    # Get Asset Tag
local asset_tag=$(whiptail --title "Enter Asset Tag" \
    --inputbox "Enter Asset Tag value. If you have not already made an Asset Tag in Salesforce, go do that now.\nDo not leave this blank.\nTag should be 6 digits long: AT-000000\nMake sure you type the AT- as well." \
    12 50 3>&1 1>&2 2>&3)
    if [[ $? -ne 0 || -z "$asset_tag" ]]; then
        error_exit "Operation canceled by user."
		exit 1
    fi

    # Get Serial Number
    local serial_number=$(sudo hdparm -I "$newdrive" | grep "Serial Number" | awk '{print $3}')
  # Insert or update the row in the database
existing_row=$(sqlite3 "$db_path" "SELECT COUNT(*) FROM drives WHERE Simulator = '$simulator' AND 'Letter/PriSec' = '$letter_prisec' AND 'Asset Tag' = '$asset_tag'")

whiptail --title "Confirm Update" \
    --yesno "Are you sure you want to add this drive to the database?\n\n$computer\n$simulator\n$letter_prisec\n$asset_tag\n$serial_number" \
    15 50 3>&1 1>&2 2>&3
confirmupdate=$?
echo $confirmupdate	
if [[ $confirmupdate -eq 0 ]]; then
	sqlite3 "$db_path" "INSERT INTO drives (Computer, Simulator, 'Letter/PriSec', 'Asset Tag', 'Serial Number') VALUES ('$computer', '$simulator', '$letter_prisec', '$asset_tag', '$serial_number');"
	whiptail --title "Success" --msgbox "Row successfully Added." 8 50
else
	error_exit "Operation canceled by user."
	exit 1
fi
}
# Function to add or update a row
update_row() {
    local simulator="$1"
    local computer="$2"
	local asset_tag="$3"
    local newdrive="$4"

    # Get Letter/PriSec
	# Display a menu to choose a letter or primary/secondary/tertiary option
	local letter_prisec=$(whiptail --title "Select Letter/PriSec" \
    --menu "Choose a Letter/PriSec value:" 15 50 8 \
    "Primary" "" \
    "Secondary" "" \
    "Tertiary" "" \
    "A" "" \
    "B" "" \
    "C" "" \
    "D" "" \
    3>&1 1>&2 2>&3)

# Check if the user canceled or no input was selected
if [[ $? -ne 0 || -z "$letter_prisec" ]]; then
    error_exit "Operation canceled by user."
    exit 1
fi
    if [[ $? -ne 0 || -z "$letter_prisec" ]]; then
        error_exit "Operation canceled by user."
		exit 1
    fi
    # Get Serial Number
    local serial_number=$(sudo hdparm -I "$newdrive" | grep "Serial Number" | awk '{print $3}')
  # Insert or update the row in the database
existing_row=$(sqlite3 "$db_path" "SELECT COUNT(*) FROM drives WHERE Simulator = '$simulator' AND 'Letter/PriSec' = '$letter_prisec' AND 'Asset Tag' = '$asset_tag'")

whiptail --title "Confirm Update" \
    --yesno "Are you sure you want to add this drive to the database?\n\n$computer\n$simulator\n$letter_prisec\n$asset_tag\n$serial_number\n$newdrive" \
    15 50 3>&1 1>&2 2>&3
confirmupdate=$?
echo $confirmupdate	
if [[ "$confirmupdate" -eq 0 ]]; then
	sqlite3 "$db_path" "UPDATE drives SET 'Asset Tag' = '$asset_tag', 'Serial Number' = '$serial_number' WHERE Simulator = '$simulator' AND 'Letter/PriSec' = '$letter_prisec';"
	whiptail --title "Success" --msgbox "Row successfully updated." 8 50
else
	error_exit "Operation canceled by user."
	exit 1
fi
}
add_or_modify_drive_to_DB(){
newdrive="$1"
echo "target drive sent is "$1""
echo add or modify
#newdrive="/dev/sda"
TMP="$(mktemp /tmp/menu.XXXXXX)"
trap "[ -f "$TMP" ] && rm -f $TMP" HUP INT QUIT TERM EXIT
echo "$DIA"
$DIA --backtitle "$msg_nchc_clonezilla" --title  \
"Drive Not found in database" --menu "Choose whether to add this drive "$newdrive" as a new drive or to modify an existing drive." 20 50 0 \
"Add new drive" "Add this drive as a brand new drive with a brand new Asset tag" \
"Modify existing Drive" "Change the serial number of an existing drive in the Database" \
2> $TMP
dmode="$(cat $TMP)"
[ -f "$TMP" ] && rm -f $TMP

case "$dmode" in
    "Add new drive")
      add_new_drive $newdrive
      ;;
    "Modify existing Drive")
      modify_existing_drive $newdrive
      ;;
  *)
    [ "$BOOTUP" = "color" ] && $SETCOLOR_FAILURE
    echo "Program terminated!"
    [ "$BOOTUP" = "color" ] && $SETCOLOR_NORMAL
    exit 1
esac

}

add_new_drive(){
newdrive="$1"    
simulator=$(select_simulator)
computer=$(select_computer "$simulator")
add_row "$simulator" "$computer" "$newdrive"
}
modify_existing_drive(){
newdrive="$1"
simulator=$(select_simulator)
computer=$(select_computer "$simulator")
at=$(select_at "$simulator" "$computer")
update_row "$simulator" "$computer" "$at" "$newdrive"

}

backup_standard_drive_action() {
	local optype=$1
	local target_dev=$2
	local backupname=$3
	local rev_num=$4
	local at=$5
	local serial_number=$6
    local simulator=$7
    local computer=$8

    if [ $optype = "savedisk" ]; then

	    $ocs_sr -q2 -c -j2 -z1p -i 4096 -sfsck -scs -senc -p true "$optype" "$backupname" "$target_dev"

	    if [ $? = "0" ]; then
        cmd="UPDATE drives SET \"Revision Number\" = '$rev_num' WHERE \"Asset Tag\" = '$at' AND \"Serial Number\" = '$serial_number';"
	    sqlite3 "$db_path" "$cmd"
    
        # sqlite3 -batch "$db_path" <<EOF 
     # BEGIN TRANSACTION;
     # UPDATE drives SET \"Revision Number\" = '$revision_number_escaped' WHERE \"Asset Tag\" = '$at_escaped' AND \"Serial Number\" = '$serial_number_escaped'; 
     # COMMIT;

    #EOF
        echo revision number set for "$at" rev num "$rev_num"
	    else
	    echo "ocs-sr did not return zero, did not modify rev number in database"
	    fi
    fi
    #just add all the stuff from get restore img to the else statement...
}


create_restore_command() {

local backupname=$1
local target_dev=$2
local rev=$3
local at=$4
local serial_number=$5
local drivetype=$6
if [[ "$drivetype" == "HDD" ]]; then
$ocs_sr -q2 -c -j2 -z1p -i 4096 -sfsck --skip-check-restorable-s -senc -p true restoredisk "$backupname" "$target_dev"
else
SD_drive_action "restoredisk" "$target_dev" "$backupname" "$rev" "$at" "$serial_number"
fi
	    if [ $? = "0" ]; then
        cmd="UPDATE drives SET \"Revision Number\" = '$rev' WHERE \"Asset Tag\" = '$at' AND \"Serial Number\" = '$serial_number';"
	    sqlite3 "$db_path" "$cmd"
        echo revision number set for "$at" rev num "$rev"
	    else
	    echo "ocs-sr did not return zero, did not modify rev number in database"
	    fi

}

log_drive_action(){
	local optype=$1
	local target_dev=$2
	local backupname=$3
	local rev_num=$4
	local at=$5
	local serial_number=$6
    local simulator=$7
    local computer=$8
	local letter_prisec=$9
	local date=${10}

    if [[ "$optype" == "restoredisk" ]]; then
    op="Restore"
    else
    op="Backup"
    fi

	baselogpath="/home/<USER>/Desktop/DriveDatabase/simulator_databases"
	log_path="$baselogpath/$simulator/$computer.db"

	local log_msg=$(whiptail --title "Enter Log Comments" \
    --inputbox "Enter log comments. Describe relevant info, such as software changes,\nthat preceded this backup or restore. " \
    12 50 3>&1 1>&2 2>&3)
    if [[ $? -ne 0 || -z "$log_msg" ]]; then
        error_exit "Operation canceled by user."
		exit 1
    fi

	sqlite3 "$log_path" "INSERT INTO logs ('Letter/PriSec', 'Asset Tag', 'Serial Number', 'Log Comment', 'Action', 'Revision Number', 'Date') VALUES ('$letter_prisec', '$at', '$serial_number', '$log_msg', '$op', '$rev_num', '$date');"
		whiptail --title "Success" --msgbox "Row successfully Added." 8 50

}


get_restore_img() {
    # Parameters
    local simulator_input=$1
    local computer_input=$2
    local drivetype=$3
    
    echo "DEBUG: get_restore_img called with: $simulator_input, $computer_input, $drivetype" >&2
    
    # Variables to track highest revision
    local highest_revision=0
    local highest_dir=""

    # Gather matching subdirectories
    local subdirs=()
    
    # Search pattern depends on drive type
    local search_pattern="/home/<USER>/${simulator_input}-${computer_input}-*-*-img"
    echo "DEBUG: Searching with pattern: $search_pattern" >&2
    
    # Gather matching subdirectories
    for subdir in $search_pattern; do
        # Skip if not a directory or doesn't exist
        if [[ ! -d "$subdir" ]]; then
            continue
        fi
        
        subdir=$(basename "$subdir")
        echo "DEBUG: Found directory: $subdir" >&2

        # Extract components using '-'
        IFS='-' read -r simulator computer date revision img <<< "$subdir"
        
        # Check if simulator and computer match user input
        if [[ "$simulator" == "$simulator_input" && "$computer" == "$computer_input" ]]; then
            # Get log info for this backup
            local log_info=""
            local baselogpath="/home/<USER>/Desktop/DriveDatabase"
            local log_path="$baselogpath/logs.db"
            
            if [[ -f "$log_path" ]]; then
                log_info=$(sqlite3 "$log_path" "SELECT Comment FROM logs WHERE 'Revision'='$revision' AND 'Computer'='$computer_input' LIMIT 1;")
            fi
            
            # Store the directory for menu selection
            if [[ -n "$log_info" ]]; then
                subdirs+=("$subdir" "rev $revision - $log_info")
            else
                subdirs+=("$subdir" "rev $revision")
            fi

            # Track the highest revision
            if (( revision > highest_revision )); then
                highest_revision="$revision"
                highest_dir="$subdir"
            fi
        fi
    done

    # Check if a highest revision was found
    if [[ -n "$highest_dir" ]]; then
        # Get log info for highest revision
        IFS='-' read -r sim comp date rev img <<< "$highest_dir"
        local log_path="/home/<USER>/Desktop/DriveDatabase/logs.db"
        local log_info=""
        
        if [[ -f "$log_path" ]]; then
            log_info=$(sqlite3 "$log_path" "SELECT Comment FROM logs WHERE 'Revision'='$rev' AND 'Computer'='$computer_input' LIMIT 1;")
        fi
        
        local menu_title="Latest revision found: $highest_revision ($highest_dir)"
        [[ -n "$log_info" ]] && menu_title="$menu_title\nLog: $log_info"
        
        use_latest=$(whiptail --title "Select a Backup Image" --menu "$menu_title" 0 0 0 \
        "Yes" "Use \"$highest_dir\"" \
        "No" "Select image from list instead" \
        3>&1 1>&2 2>&3)
        
        if [[ $use_latest = "Yes" ]]; then
            IFS='-' read -r simulator computer date rev img <<< "$highest_dir"
            echo "$highest_dir" "$rev"
            return 0
        fi
    fi

    # Check if there are any matching directories
    if [[ ${#subdirs[@]} -eq 0 ]]; then
        whiptail --msgbox "No matching Backups found for $simulator_input-$computer_input!" 35 105
        exit 1
    fi

    # Show menu of matching directories
    selected=$(whiptail --title "Select a Backup Image" --menu "Choose a backup image:" 20 80 10 "${subdirs[@]}" 3>&1 1>&2 2>&3)

    # Exit if the user cancels
    if [[ $? -ne 0 ]]; then
        echo "No selection made."
        exit 1
    fi

    # Output the selected directory
    IFS='-' read -r simulator computer date rev img <<< "$selected"
    echo "$selected" "$rev"
}

# Function to perform dd operations (save or restore disk)
SD_drive_action() {
    local optype="$1"
    local target_dev="$2"
    local backupname="$3"
    local rev_num="$4"
    local at="$5"
    local serial_number="$6"

    # Verify target_dev is a block device
    if [ ! -b "$target_dev" ]; then
        echo "Error: $target_dev is not a valid block device"
        return 1
    fi
    # Perform dd operation, converting \r to \n
    if [ "$optype" = "savedisk" ]; then
        dd if="$target_dev" of="/home/<USER>/$backupname" bs=4M status=progress
    else
        # Assume restore operation
        if [ ! -f "/home/<USER>/$backupname" ]; then
            echo "Error: Backup file /home/<USER>/$backupname not found"
            return 1
        fi
        dd if="/home/<USER>/$backupname" of="$target_dev" bs=4M status=progress
    fi
}
